"use client"

import { useLanguage } from "@/components/providers/language-provider"

export function StructuredData() {
  const { language } = useLanguage()

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "RNG STORE",
    alternateName: ["RNG HACK", "PUBG Mobile Emulator Bypass"],
    url: "https://rngstore.vip",
    description: "Professional RNG hack for PUBG mobile emulator bypass. Play PUBG mobile on PC undetected with safe Android emulator bypass tools.",
    potentialAction: {
      "@type": "SearchAction",
      target: "https://rngstore.vip/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    publisher: {
      "@type": "Organization",
      name: "RNG STORE",
      url: "https://rngstore.vip",
      logo: {
        "@type": "ImageObject",
        url: "https://rngstore.vip/mido-logo.jpg"
      }
    }
  }

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "<PERSON><PERSON> STORE",
    url: "https://rngstore.vip",
    logo: "https://rngstore.vip/mido-logo.jpg",
    description: "Premium gaming store for PUBG hacks, RNG bypass tools, and gaming accounts",
    contactPoint: {
      "@type": "ContactPoint",
      contactType: "customer service",
      availableLanguage: ["English", "Arabic"]
    },
    sameAs: [
      "https://twitter.com/rngstore",
      "https://facebook.com/rngstore"
    ]
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
    </>
  )
}

interface ProductStructuredDataProps {
  product: {
    id: string
    name: { en: string; ar: string }
    description: { en: string; ar: string }
    price: number
    imageUrl: string
    status: string
  }
}

export function ProductStructuredData({ product }: ProductStructuredDataProps) {
  const { language } = useLanguage()

  const productSchema = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: product.name[language],
    description: product.description[language],
    image: `https://rngstore.vip${product.imageUrl}`,
    brand: {
      "@type": "Brand",
      name: "RNG STORE"
    },
    offers: {
      "@type": "Offer",
      price: product.price.toString(),
      priceCurrency: "USD",
      availability: product.status === "active" 
        ? "https://schema.org/InStock" 
        : "https://schema.org/OutOfStock",
      seller: {
        "@type": "Organization",
        name: "RNG STORE"
      }
    }
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(productSchema)
      }}
    />
  )
}

export function BreadcrumbStructuredData({ 
  items 
}: { 
  items: Array<{ name: string; url: string }> 
}) {
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: `https://rngstore.vip${item.url}`
    }))
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(breadcrumbSchema)
      }}
    />
  )
}
