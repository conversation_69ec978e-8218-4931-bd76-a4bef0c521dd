/**
 * SEO Utilities for RNG STORE
 * Comprehensive SEO helper functions for multilingual gaming website
 */

export interface BilingualContent {
  en: string
  ar: string
}

export interface SEOContent {
  title: BilingualContent
  description: BilingualContent
  keywords: BilingualContent
}

// Semantic keyword variations for natural SEO optimization
export const SEMANTIC_KEYWORDS = {
  en: {
    rng: ["emulator bypass technology", "gaming enhancement tools", "PC gaming solutions"],
    pubg: ["mobile gaming utilities", "competitive gaming tools", "gaming performance enhancers"],
    emulator: ["PC gaming compatibility", "mobile-to-PC gaming", "cross-platform gaming"],
    accounts: ["premium gaming profiles", "high-level gaming accounts", "competitive gaming marketplace"],
    general: ["gaming utilities", "performance tools", "gaming enhancements"]
  },
  ar: {
    rng: ["هاك RNG", "تجاوز RNG", "تجاوز محاكي RNG"],
    pubg: ["هاكات ببجي", "هاك ببجي موبايل", "تحميل هاك ببجي", "ايمبوت ببجي"],
    emulator: ["تجاوز محاكي الاندرويد", "تجاوز كشف المحاكي", "ببجي موبايل على الكمبيوتر"],
    accounts: ["حسابات ببجي للبيع", "حسابات الألعاب المميزة", "سوق حسابات الألعاب"],
    general: ["هاكات الألعاب", "هاكات ألعاب الموبايل", "هاكات غير قابلة للكشف"]
  }
}

// Generate SEO-optimized title with keywords
export function generateSEOTitle(
  baseTitle: BilingualContent,
  category?: keyof typeof PRIMARY_KEYWORDS.en,
  language: 'en' | 'ar' = 'en'
): string {
  const title = baseTitle[language]
  
  if (category && PRIMARY_KEYWORDS[language][category]) {
    const keywords = PRIMARY_KEYWORDS[language][category]
    const mainKeyword = keywords[0]
    
    // Ensure title includes primary keyword if not already present
    if (!title.toLowerCase().includes(mainKeyword.toLowerCase())) {
      return language === 'en' 
        ? `${title} | ${mainKeyword}`
        : `${title} | ${mainKeyword}`
    }
  }
  
  return title
}

// Generate SEO-optimized description with natural keyword integration
export function generateSEODescription(
  baseDescription: BilingualContent,
  keywords: string[],
  language: 'en' | 'ar' = 'en'
): string {
  const description = baseDescription[language]
  const keywordString = keywords.slice(0, 3).join(', ')
  
  // Ensure description is within optimal length (150-160 characters)
  const maxLength = 155
  
  if (description.length > maxLength) {
    return description.substring(0, maxLength - 3) + '...'
  }
  
  // Add keywords naturally if space allows
  const withKeywords = `${description} ${keywordString}`
  if (withKeywords.length <= maxLength) {
    return withKeywords
  }
  
  return description
}

// Generate structured data for products
export function generateProductStructuredData(product: {
  id: string
  name: BilingualContent
  description: BilingualContent
  price: number
  imageUrl: string
  category: string
  availability: 'active' | 'sold' | 'inactive'
}, language: 'en' | 'ar' = 'en') {
  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "@id": `https://rngstore.vip/product/${product.id}`,
    name: product.name[language],
    alternateName: product.name[language === 'en' ? 'ar' : 'en'],
    description: product.description[language],
    image: `https://rngstore.vip${product.imageUrl}`,
    category: product.category,
    brand: {
      "@type": "Brand",
      name: "RNG STORE"
    },
    offers: {
      "@type": "Offer",
      price: product.price,
      priceCurrency: "USD",
      availability: product.availability === 'active' 
        ? "https://schema.org/InStock" 
        : "https://schema.org/OutOfStock",
      seller: {
        "@type": "Organization",
        name: "RNG STORE",
        url: "https://rngstore.vip"
      }
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.8",
      reviewCount: "150",
      bestRating: "5",
      worstRating: "1"
    }
  }
}

// Generate FAQ structured data for better SERP features
export function generateFAQStructuredData(faqs: Array<{
  question: BilingualContent
  answer: BilingualContent
}>, language: 'en' | 'ar' = 'en') {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map(faq => ({
      "@type": "Question",
      name: faq.question[language],
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer[language]
      }
    }))
  }
}

// Generate breadcrumb structured data
export function generateBreadcrumbStructuredData(
  breadcrumbs: Array<{ name: BilingualContent; url: string }>,
  language: 'en' | 'ar' = 'en'
) {
  const baseUrl = "https://rngstore.vip"
  const langPrefix = language === 'ar' ? '/ar' : ''
  
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: crumb.name[language],
      item: `${baseUrl}${langPrefix}${crumb.url}`
    }))
  }
}

// Optimize content for search engines
export function optimizeContentForSEO(
  content: string,
  targetKeywords: string[],
  language: 'en' | 'ar' = 'en'
): string {
  let optimizedContent = content
  
  // Ensure primary keyword appears in first 100 characters
  const primaryKeyword = targetKeywords[0]
  if (!optimizedContent.substring(0, 100).toLowerCase().includes(primaryKeyword.toLowerCase())) {
    optimizedContent = `${primaryKeyword} - ${optimizedContent}`
  }
  
  // Add semantic keywords naturally
  const semanticKeywords = targetKeywords.slice(1, 4)
  semanticKeywords.forEach(keyword => {
    if (!optimizedContent.toLowerCase().includes(keyword.toLowerCase())) {
      // Add keyword naturally in context
      const sentences = optimizedContent.split('. ')
      if (sentences.length > 1) {
        sentences[1] = `${sentences[1]} ${keyword}`
        optimizedContent = sentences.join('. ')
      }
    }
  })
  
  return optimizedContent
}

// Generate canonical URL based on language and path
export function generateCanonicalURL(
  path: string,
  language: 'en' | 'ar' = 'en'
): string {
  const baseUrl = "https://rngstore.vip"
  const cleanPath = path.replace(/^\/ar/, '') || '/'
  
  return language === 'ar' 
    ? `${baseUrl}/ar${cleanPath}`
    : `${baseUrl}${cleanPath}`
}

// Generate hreflang URLs for a given path
export function generateHreflangURLs(path: string) {
  const baseUrl = "https://rngstore.vip"
  const cleanPath = path.replace(/^\/ar/, '') || '/'
  
  return {
    en: `${baseUrl}${cleanPath}`,
    ar: `${baseUrl}/ar${cleanPath}`,
    'x-default': `${baseUrl}${cleanPath}`
  }
}

// Validate and optimize meta description length
export function optimizeMetaDescription(
  description: string,
  maxLength: number = 155
): string {
  if (description.length <= maxLength) {
    return description
  }
  
  // Find last complete sentence within limit
  const truncated = description.substring(0, maxLength)
  const lastSentence = truncated.lastIndexOf('. ')
  
  if (lastSentence > maxLength * 0.7) {
    return description.substring(0, lastSentence + 1)
  }
  
  // Find last complete word
  const lastSpace = truncated.lastIndexOf(' ')
  return description.substring(0, lastSpace) + '...'
}

// Generate social media meta tags
export function generateSocialMetaTags(content: {
  title: string
  description: string
  image: string
  url: string
}) {
  return {
    openGraph: {
      title: content.title,
      description: content.description,
      url: content.url,
      images: [
        {
          url: content.image,
          width: 1200,
          height: 630,
          alt: content.title,
        },
      ],
      type: 'website',
      siteName: 'RNG STORE',
    },
    twitter: {
      card: 'summary_large_image',
      title: content.title,
      description: content.description,
      images: [content.image],
      creator: '@rngstore',
    },
  }
}

// Calculate keyword density
export function calculateKeywordDensity(
  content: string,
  keyword: string
): number {
  const words = content.toLowerCase().split(/\s+/)
  const keywordWords = keyword.toLowerCase().split(/\s+/)
  const keywordCount = words.filter(word => 
    keywordWords.includes(word)
  ).length
  
  return (keywordCount / words.length) * 100
}

// SEO content validation
export function validateSEOContent(content: {
  title: string
  description: string
  content: string
  keywords: string[]
}) {
  const issues: string[] = []
  
  // Title validation
  if (content.title.length < 30 || content.title.length > 60) {
    issues.push('Title should be between 30-60 characters')
  }
  
  // Description validation
  if (content.description.length < 120 || content.description.length > 155) {
    issues.push('Meta description should be between 120-155 characters')
  }
  
  // Keyword density validation
  content.keywords.forEach(keyword => {
    const density = calculateKeywordDensity(content.content, keyword)
    if (density > 3) {
      issues.push(`Keyword "${keyword}" density too high: ${density.toFixed(1)}%`)
    }
  })
  
  return {
    isValid: issues.length === 0,
    issues
  }
}
