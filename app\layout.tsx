import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Cairo, Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/providers/theme-provider"
import { LanguageProvider } from "@/components/providers/language-provider"
import { Navigation } from "@/components/layout/navigation"
import { Footer } from "@/components/layout/footer"
import { StructuredData } from "@/components/seo/structured-data"

const inter = Inter({ subsets: ["latin"], variable: "--font-inter" })
const cairo = Cairo({ subsets: ["arabic", "latin"], variable: "--font-cairo" })

export const metadata: Metadata = {
  title: "RNG STORE - Professional Gaming Tools & Premium PUBG Accounts",
  description:
    "Professional gaming tools and premium PUBG accounts marketplace. Advanced emulator bypass technology, gaming utilities, and high-level accounts with rare skins. Safe, reliable solutions for enhanced gaming experience.",
  keywords: "gaming tools, PUBG mobile emulator bypass, premium gaming accounts, RNG technology, gaming utilities, PUBG accounts, emulator bypass, gaming marketplace",
  authors: [{ name: "<PERSON>NG STORE" }],
  creator: "<PERSON>NG STORE",
  publisher: "RNG STORE",
  robots: "index, follow",
  alternates: {
    languages: {
      'en': 'https://rngstore.vip',
      'ar': 'https://rngstore.vip/ar',
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://rngstore.vip",
    title: "RNG STORE - Professional Gaming Tools & Premium PUBG Accounts",
    description:
      "Professional gaming tools and premium PUBG accounts marketplace. Advanced emulator bypass technology and high-level accounts for enhanced gaming experience.",
    siteName: "RNG STORE",
    images: [
      {
        url: "https://rngstore.vip/mido-logo.jpg",
        width: 1200,
        height: 630,
        alt: "RNG STORE - PUBG Mobile Emulator Bypass & Gaming Accounts",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "RNG STORE - Professional Gaming Tools & Premium PUBG Accounts",
    description:
      "Professional gaming tools and premium PUBG accounts marketplace. Advanced emulator bypass technology and high-level accounts for enhanced gaming experience.",
    images: ["https://rngstore.vip/mido-logo.jpg"],
    creator: "@rngstore",
    site: "@rngstore",
  },
  icons: {
    icon: '/mido-logo.jpg',
    shortcut: '/mido-logo.jpg',
    apple: '/mido-logo.jpg',
  },
  generator: 'RNG STORE'
}

export default function RootLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode
  params: { locale: string }
}) {
  return (
    <html lang={locale || "en"} suppressHydrationWarning>
      <head>
        <link rel="canonical" href="https://rngstore.vip" />
        <link rel="sitemap" href="/sitemap.xml" />
        <link rel="icon" href="/mido-logo.jpg" />
        <meta name="theme-color" content="#ea580c" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

        {/* Hreflang tags for multilingual SEO */}
        <link rel="alternate" hrefLang="en" href="https://rngstore.vip" />
        <link rel="alternate" hrefLang="ar" href="https://rngstore.vip/ar" />
        <link rel="alternate" hrefLang="x-default" href="https://rngstore.vip" />

        {/* Preconnect to external domains for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* DNS prefetch for better performance */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />

      </head>
      <body className={`${inter.variable} ${cairo.variable} font-sans antialiased bg-zinc-900 text-white`}>
        <ThemeProvider>
          <LanguageProvider locale={locale}>
            <StructuredData />
            <div className="min-h-screen flex flex-col">
              <Navigation />
              <main className="flex-1">{children}</main>
              <Footer />
            </div>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
