import type { Metada<PERSON> } from "next"
import { HeroSec<PERSON> } from "@/components/home/<USER>"
import { FeaturedProducts } from "@/components/home/<USER>"
import { WhyChooseUs } from "@/components/home/<USER>"
import { Testimonials } from "@/components/home/<USER>"
import { Account } from "@/types/account"
import { Hack } from "@/types/hack"
import { CTASection } from "@/components/home/<USER>"

export const metadata: Metadata = {
  title: "RNG VIP - Official RNG VIP Store | Download RNG VIP Tools & Premium Accounts",
  description:
    "RNG VIP official store - Download RNG VIP emulator bypass tools and premium gaming accounts. Best RNG VIP solutions for PUBG mobile with guaranteed safety.",
  keywords: "RNG VIP, RNG VIP store, RNG VIP download, RNG VIP tools, RNG VIP official, PUBG mobile emulator bypass",
  openGraph: {
    title: "RNG STORE - Professional Gaming Tools & Premium PUBG Accounts",
    description:
      "Professional gaming tools and premium PUBG accounts. Advanced emulator bypass technology and high-level accounts for enhanced gaming experience.",
    url: "https://rngstore.vip",
    images: [
      {
        url: "/mido-logo.jpg",
        width: 1200,
        height: 630,
        alt: "RNG HACK - PUBG Mobile Emulator Bypass",
      },
    ],
  },
}

/**
 * Get Featured Accounts from the accounts page data
 */
const getFeaturedAccounts = async (): Promise<Account[]> => {
  // Import the getAccounts function from accounts page
  const getAccountsModule = await import('./accounts/page')
  const allAccounts = await getAccountsModule.getAccounts({})

  // Filter for featured accounts and limit to 3
  return allAccounts.filter(account => account.isFeatured).slice(0, 3)
}

/**
 * Get Featured Hacks from the hacks page data
 */
const getFeaturedHacks = async (): Promise<Hack[]> => {
  // Import the getHacks function from hacks page
  const getHacksModule = await import('./hacks/page')
  const allHacks = await getHacksModule.getHacks({})

  // Filter for featured hacks and limit to 3
  return allHacks.filter(hack => hack.isFeatured).slice(0, 3)
}

/**
 * SSR-Ready Featured Products Data Fetcher
 *
 * This function fetches real data from the accounts and hacks pages,
 * ensuring consistency across the website.
 */
const getFeaturedProducts = async (): Promise<{
  accounts: Account[];
  hacks: Hack[];
}> => {
  // Get real featured data from accounts and hacks pages
  const [accounts, hacks] = await Promise.all([
    getFeaturedAccounts(),
    getFeaturedHacks()
  ])

  return {
    accounts,
    hacks
  }
}

export default async function HomePage() {
  const featuredProducts = await getFeaturedProducts()

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "Store",
    name: "RNG STORE",
    alternateName: ["RNG HACK"],
    description: "Professional RNG hack for PUBG mobile emulator bypass. Play PUBG mobile on PC undetected with safe Android emulator bypass tools.",
    url: "https://rngstore.vip",
    logo: "https://rngstore.vip/mido-logo.jpg",
    image: "https://rngstore.vip/hero.jpg",
    keywords: ["RNG hack", "PUBG mobile emulator bypass", "Android emulator hack", "emulator detection bypass", "PUBG mobile on PC"],
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "RNG Gaming Tools",
      description: "Professional gaming tools for PUBG mobile emulator bypass",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "SoftwareApplication",
            name: "RNG HACK - PUBG Mobile Emulator Bypass",
            description: "Professional RNG bypass tool for playing PUBG mobile on PC emulator undetected",
            applicationCategory: "GameApplication",
            operatingSystem: ["Windows", "Android"],
            keywords: ["RNG hack", "emulator bypass", "PUBG mobile hack", "Android emulator"],
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Product",
            name: "PUBG Premium Accounts",
            description: "High-level PUBG accounts with rare skins and items",
            category: "Gaming Accounts",
          },
        },
      ],
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.8",
      reviewCount: "10000",
      bestRating: "5",
      worstRating: "1"
    }
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
      <HeroSection />
      <FeaturedProducts products={featuredProducts} />
      <WhyChooseUs />
      <Testimonials />
      <CTASection />
    </>
  )
}
