import type { Metadata } from "next"
import { Account } from "@/types/account"
import { AccountsPageContent } from "@/components/accounts/accounts-page-content"

export const metadata: Metadata = {
  title: "Premium Gaming Accounts - High-Level PUBG Accounts with Rare Items",
  description:
    "Premium gaming accounts marketplace featuring high-level PUBG accounts with rare skins, exclusive items, and competitive ranks. Secure transactions with instant delivery and account verification.",
  keywords: "premium gaming accounts, PUBG accounts, high level accounts, rare skins, gaming marketplace, account trading, competitive gaming",
  openGraph: {
    title: "Premium PUBG Mobile Accounts for Sale | High Level Accounts with Rare Skins",
    description:
      "Buy premium PUBG mobile accounts with high levels, rare skins, and exclusive items. Perfect for players using RNG hack and emulator bypass.",
    url: "https://rngstore.vip/accounts",
    images: [
      {
        url: "/mido-logo.jpg",
        width: 1200,
        height: 630,
        alt: "Premium PUBG Mobile Accounts - RNG STORE",
      },
    ],
  },
}

/**
 * SSR-Ready Accounts Data Fetcher with Advanced Filtering
 *
 * This function implements server-side data fetching for the accounts page with
 * comprehensive filtering and search capabilities. Optimized for SEO and performance.
 *
 * TODO: Replace with Supabase queries:
 * - Base Query: SELECT * FROM accounts WHERE status = 'active'
 * - Filters: price_range, level_range, platform, has_skins, rank
 * - Search: Full-text search on name and description (multilingual)
 * - Sorting: price, level, created_at, popularity
 * - Pagination: Cursor-based for better performance
 *
 * Expected API Integration:
 * - Database: Supabase PostgreSQL with JSONB for multilingual content
 * - Caching: Redis for popular filter combinations (TTL: 5 minutes)
 * - Search: PostgreSQL GIN indexes for fast text search
 * - Images: Cloudflare CDN with WebP conversion
 * - Analytics: Track popular accounts for recommendation engine
 */
export const getAccounts = async (searchParams: {
  price_min?: string;
  price_max?: string;
  level_min?: string;
  search?: string;
  platform?: string;
  sort?: string;
  page?: string;
}): Promise<Account[]> => {
  // TODO: Implement real database fetching with advanced filters
  // Example Supabase implementation:
  // let query = supabase
  //   .from('accounts')
  //   .select('*')
  //   .eq('status', 'active');

  // if (searchParams.price_min) {
  //   query = query.gte('price', parseFloat(searchParams.price_min));
  // }
  // if (searchParams.price_max) {
  //   query = query.lte('price', parseFloat(searchParams.price_max));
  // }
  // if (searchParams.search) {
  //   query = query.or(`name->>en.ilike.%${searchParams.search}%,name->>ar.ilike.%${searchParams.search}%`);
  // }

  // const { data: accounts } = await query;
  // return accounts || [];

  // SEO-Optimized Accounts Data with Target Keywords
  return [
    {
      id: "1",
      name: {
        en: "Premium PUBG Mobile Account - Conqueror Rank with Rare Skins",
        ar: "حساب ببجي موبايل مميز - رتبة كونكرر مع اسكنات نادرة"
      },
      description: {
        en: "Premium PUBG mobile account for sale with Conqueror rank and exclusive rare skins. Features mythic outfits, legendary weapon skins, and premium vehicle skins. High-level gaming account with competitive achievements, rare titles, and exclusive items. Perfect for serious PUBG mobile players looking for premium gaming experience.",
        ar: "حساب ببجي موبايل مميز للبيع برتبة كونكرر واسكنات نادرة حصرية. يتميز بملابس أسطورية واسكنات أسلحة نادرة واسكنات مركبات مميزة. حساب ألعاب عالي المستوى مع إنجازات تنافسية وألقاب نادرة وعناصر حصرية."
      },
      price: 449.99,
      status: "active" as const,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z",
      imageUrl: "/mido-logo.jpg",
      isFeatured: true
    },
    {
      id: "2",
      name: {
        en: "PUBG PC Elite Account - Steam with Rare Weapon Skins",
        ar: "حساب PUBG PC النخبة - ستيم مع اسكنات أسلحة نادرة"
      },
      description: {
        en: "Elite PUBG PC Steam account for sale with rare weapon skins and exclusive items. Professional gaming account with competitive ranking history, premium crates, and legendary skins collection. Features rare AKM, M416, and AWM skins. Perfect for competitive PUBG PC players seeking premium gaming accounts.",
        ar: "حساب PUBG PC ستيم نخبة للبيع مع اسكنات أسلحة نادرة وعناصر حصرية. حساب ألعاب احترافي مع تاريخ تصنيف تنافسي وصناديق مميزة ومجموعة اسكنات أسطورية. يتضمن اسكنات نادرة لـ AKM و M416 و AWM."
      },
      price: 699.99,
      status: "active" as const,
      createdAt: "2024-01-10T14:20:00Z",
      updatedAt: "2024-01-10T14:20:00Z",
      imageUrl: "/mido-logo.jpg",
      isFeatured: true
    },
    {
      id: "3",
      name: {
        en: "PUBG Mobile Starter Account - Crown Rank with Skins",
        ar: "حساب ببجي موبايل للمبتدئين - رتبة كراون مع اسكنات"
      },
      description: {
        en: "Affordable PUBG mobile starter account with Crown rank and good skin collection. Gaming account perfect for new players with premium outfits, weapon skins, and vehicle customizations. Great value gaming account for beginners looking to advance quickly in PUBG mobile.",
        ar: "حساب ببجي موبايل للمبتدئين بسعر معقول برتبة كراون ومجموعة اسكنات جيدة. حساب ألعاب مثالي للاعبين الجدد مع ملابس مميزة واسكنات أسلحة وتخصيصات مركبات."
      },
      price: 199.99,
      status: "active" as const,
      createdAt: "2024-01-05T09:15:00Z",
      updatedAt: "2024-01-20T16:45:00Z",
      imageUrl: "/mido-logo.jpg",
      isFeatured: false
    },
    {
      id: "4",
      name: {
        en: "PUBG Mobile VIP Account - Mythic Outfits Collection",
        ar: "حساب ببجي موبايل VIP - مجموعة ملابس أسطورية"
      },
      description: {
        en: "Exclusive PUBG mobile VIP account with mythic outfits and legendary items. Premium gaming account featuring rare mythic sets, exclusive weapon finishes, and limited-time items. High-value account for collectors and competitive players seeking the ultimate PUBG mobile experience.",
        ar: "حساب ببجي موبايل VIP حصري مع ملابس أسطورية وعناصر نادرة. حساب ألعاب مميز يتضمن مجموعات أسطورية نادرة وتشطيبات أسلحة حصرية وعناصر محدودة الوقت."
      },
      price: 899.99,
      status: "active" as const,
      createdAt: "2024-01-20T12:30:00Z",
      updatedAt: "2024-01-20T12:30:00Z",
      imageUrl: "/mido-logo.jpg",
      isFeatured: true
    },
    {
      id: "5",
      name: {
        en: "PUBG PC Professional Account - Tournament Ready",
        ar: "حساب PUBG PC احترافي - جاهز للبطولات"
      },
      description: {
        en: "Professional PUBG PC account ready for tournaments and competitive play. Features high-tier ranking, extensive skin collection, and tournament experience. Premium Steam account with rare items, exclusive skins, and competitive achievements perfect for esports players.",
        ar: "حساب PUBG PC احترافي جاهز للبطولات واللعب التنافسي. يتميز بتصنيف عالي المستوى ومجموعة اسكنات واسعة وخبرة في البطولات. حساب ستيم مميز مع عناصر نادرة واسكنات حصرية."
      },
      price: 1299.99,
      status: "active" as const,
      createdAt: "2024-01-22T15:45:00Z",
      updatedAt: "2024-01-22T15:45:00Z",
      imageUrl: "/mido-logo.jpg",
      isFeatured: true
    }
  ]
}

export default async function AccountsPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const accounts = await getAccounts(searchParams)

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: "PUBG Accounts",
    description: "Premium PUBG gaming accounts collection",
    url: "https://pubgstore.com/accounts",
    mainEntity: {
      "@type": "ItemList",
      itemListElement: accounts.map((account, index) => ({
        "@type": "Product",
        position: index + 1,
        name: account.name.en,
        description: account.description.en,
        offers: {
          "@type": "Offer",
          price: account.price,
          priceCurrency: "USD",
          availability: account.status === "active" ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
        },
      })),
    },
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
      <AccountsPageContent accounts={accounts} />
    </>
  )
}
