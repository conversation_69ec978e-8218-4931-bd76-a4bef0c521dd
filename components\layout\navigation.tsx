"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, X, User, Globe } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useLanguage } from "@/components/providers/language-provider"

const navItems = [
  { href: "/", label: { en: "Home", ar: "الرئيسية" } },
  { href: "/هاك-باي-باس-ببجي", label: { en: "Gaming Tools", ar: "أدوات الألعاب" } },
  { href: "/rng-vip", label: { en: "Professional Tools", ar: "الأدوات الاحترافية" } },
  { href: "/accounts", label: { en: "Premium Accounts", ar: "الحسابات المميزة" } },
  { href: "/hacks", label: { en: "Gaming Utilities", ar: "المرافق" } },
  { href: "/profile", label: { en: "Profile", ar: "الملف الشخصي" } },
]

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()
  const { language, setLanguage, t, isHydrated } = useLanguage()

  const toggleLanguage = () => {
    setLanguage(language === "en" ? "ar" : "en")
  }

  // Use server-safe language until hydrated to prevent mismatch
  const displayLanguage = isHydrated ? language : "en"

  return (
    <nav className="bg-black/95 backdrop-blur-sm border-b border-zinc-800 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className={`flex items-center ${displayLanguage === "ar" ? "space-x-reverse space-x-2" : "space-x-2"}`}>
            <img
              src="/mido-logo.jpg"
              alt="RNG STORE"
              className="w-8 h-8 rounded-lg object-cover"
            />
            <span className="text-xl font-bold text-white">RNG STORE</span>
          </Link>

          {/* Desktop Navigation */}
          <div className={`hidden md:flex items-center ${displayLanguage === "ar" ? "space-x-reverse space-x-6" : "space-x-8"}`}>
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`text-sm font-medium transition-colors hover:text-orange-400 ${
                  pathname === item.href ? "text-orange-400" : "text-zinc-300"
                } ${displayLanguage === "ar" ? "px-2" : ""}`}
              >
                {item.label[displayLanguage]}
              </Link>
            ))}
          </div>

          {/* Right Side Actions */}
          <div className={`hidden md:flex items-center ${displayLanguage === "ar" ? "space-x-reverse space-x-4" : "space-x-4"}`}>
            <Button variant="ghost" size="sm" onClick={toggleLanguage} className="text-zinc-300 hover:text-white">
              <Globe className={`w-4 h-4 ${displayLanguage === "ar" ? "ml-2" : "mr-2"}`} />
              {displayLanguage.toUpperCase()}
            </Button>
            <Link href="/auth">
              <Button variant="ghost" size="sm" className="text-zinc-300 hover:text-white">
                <User className={`w-4 h-4 ${displayLanguage === "ar" ? "ml-2" : "mr-2"}`} />
                {t("login")}
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden text-zinc-300 hover:text-white"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="md:hidden border-t border-zinc-800 py-4"
            >
              <div className="flex flex-col space-y-4">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`text-sm font-medium transition-colors hover:text-orange-400 ${
                      pathname === item.href ? "text-orange-400" : "text-zinc-300"
                    } ${displayLanguage === "ar" ? "text-right" : "text-left"}`}
                    onClick={() => setIsOpen(false)}
                  >
                    {item.label[displayLanguage]}
                  </Link>
                ))}
                <div className={`flex items-center justify-between pt-4 border-t border-zinc-800 ${displayLanguage === "ar" ? "flex-row-reverse" : ""}`}>
                  <Button variant="ghost" size="sm" onClick={toggleLanguage} className="text-zinc-300 hover:text-white">
                    <Globe className={`w-4 h-4 ${displayLanguage === "ar" ? "ml-2" : "mr-2"}`} />
                    {displayLanguage.toUpperCase()}
                  </Button>
                  <Link href="/auth">
                    <Button variant="ghost" size="sm" className="text-zinc-300 hover:text-white">
                      <User className={`w-4 h-4 ${displayLanguage === "ar" ? "ml-2" : "mr-2"}`} />
                      {t("login")}
                    </Button>
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </nav>
  )
}
