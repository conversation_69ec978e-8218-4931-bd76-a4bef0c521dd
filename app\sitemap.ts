import type { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = "https://rngstore.vip"
  const currentDate = new Date()

  // Updated: 2025-07-13 - Fixed to only include real pages (no 404s)
  // Deployment with correct Git author: <EMAIL>

  // ONLY PAGES THAT ACTUALLY EXIST ON YOUR LIVE SITE
  return [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: "daily" as const,
      priority: 1.0,
    },
    {
      url: `${baseUrl}/hacks`,
      lastModified: currentDate,
      changeFrequency: "daily" as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/accounts`,
      lastModified: currentDate,
      changeFrequency: "daily" as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/profile`,
      lastModified: currentDate,
      changeFrequency: "weekly" as const,
      priority: 0.5,
    },
    {
      url: `${baseUrl}/auth`,
      lastModified: currentDate,
      changeFrequency: "monthly" as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/rng-vip`,
      lastModified: currentDate,
      changeFrequency: "daily" as const,
      priority: 1.0,
    },
    {
      url: `${baseUrl}/pubg-mobile-emulator-bypass-2025`,
      lastModified: currentDate,
      changeFrequency: "weekly" as const,
      priority: 0.98,
    },
    {
      url: `${baseUrl}/هاك-باي-باس-ببجي`,
      lastModified: currentDate,
      changeFrequency: "daily" as const,
      priority: 0.99,
    },
  ]
}
