import type { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = "https://rngstore.vip"
  const currentDate = new Date()

  // Updated: 2025-07-13 - Fixed to only include real pages (no 404s)
  // Deployment with correct Git author: <EMAIL>

  // CLEAN AND SIMPLE - ONLY ESSENTIAL PAGES
  return [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: "daily" as const,
      priority: 1.0,
    },
    {
      url: `${baseUrl}/hacks`,
      lastModified: currentDate,
      changeFrequency: "daily" as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/accounts`,
      lastModified: currentDate,
      changeFrequency: "daily" as const,
      priority: 0.9,
    },
  ]
}
