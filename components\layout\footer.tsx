"use client"

import Link from "next/link"
import { Facebook, Twitter, Instagram, Youtube } from "lucide-react"
import { useLanguage } from "@/components/providers/language-provider"

export function Footer() {
  const { language, t } = useLanguage()

  const footerLinks = {
    products: {
      title: { en: "Products", ar: "المنتجات" },
      links: [
        { href: "/rng-vip", label: { en: "Professional Tools", ar: "الأدوات الاحترافية" } },
        { href: "/accounts", label: { en: "Gaming Accounts", ar: "حسابات الألعاب" } },
        { href: "/hacks", label: { en: "Gaming Utilities", ar: "أدوات الألعاب" } },
      ],
    },
  }

  return (
    <footer className="bg-black border-t border-zinc-800">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <img
                src="/mido-logo.jpg"
                alt="RNG STORE"
                className="w-8 h-8 rounded-lg object-cover"
              />
              <span className="text-xl font-bold text-white">RNG STORE</span>
            </div>
            <p className="text-zinc-400 text-sm">
              {language === "en"
                ? "Your trusted source for RNG hacks, PUBG mobile emulator bypass, and premium gaming tools."
                : "مصدرك الموثوق لهاكات RNG وتجاوز محاكي PUBG موبايل وأدوات الألعاب المميزة."}
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-zinc-400 hover:text-orange-400 transition-colors">
                <Facebook className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-zinc-400 hover:text-orange-400 transition-colors">
                <Twitter className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-zinc-400 hover:text-orange-400 transition-colors">
                <Instagram className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-zinc-400 hover:text-orange-400 transition-colors">
                <Youtube className="w-5 h-5" />
              </Link>
            </div>
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([key, section]) => (
            <div key={key} className="space-y-4">
              <h3 className="text-white font-semibold">{section.title[language]}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.href}>
                    <Link href={link.href} className="text-zinc-400 hover:text-orange-400 transition-colors text-sm">
                      {link.label[language]}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="border-t border-zinc-800 mt-12 pt-8 text-center">
          <p className="text-zinc-400 text-sm">
            © 2024 RNG STORE. {language === "en" ? "All rights reserved." : "جميع الحقوق محفوظة."}
          </p>
        </div>
      </div>
    </footer>
  )
}
